{"START_PROGRAM": "%1 when program start", "CONTROL_IF": "if %1 then", "CONTROL_IF_ELSE": "else", "CONTROL_REPEAT": "repeat", "CONTROL_TIMES": "times", "CONTROL_FOREVER": "forever", "CONTROL_WHILE": "while", "CONTROL_UNTIL": "wait until", "CONTROL_FOR": "for", "CONTROL_FROM": "from", "CONTROL_TO": "to", "CONTROL_BY": "by", "CONTROL_BREAK": "break", "CONTROL_CONTINUE": "continue", "CONTROL_SET_RANGE": "set range", "CONTROL_LOW": "low", "CONTROL_HIGH": "high", "CONTROL_WAIT": "wait", "CONTROL_SECONDS": "seconds", "PRINT": "print", "TEXT_JOIN": "join", "BEE_LED_ON": "%5 %1 on with red %2 green %3 blue %4", "BEE_LED_OFF": "%2 %1 off", "BEE_PRESS_BUTTON_START": "%3 button %1 is %2", "BEE_BUTTON_PRESSED": "pressed", "BEE_BUTTON_RELEASED": "released", "BEE_BUTTON_LONG_PRESSED": "long pressed", "BEE_BUTTON_STATUS": "%3 button %1 is %2?", "BEE_GPIO_READ": "%2 %1 digital read", "BEE_ANALOG_READ": "%2 %1 analog read", "BEE_GYRO_CALIBRATE": "%1 calibrate", "BEE_GYRO_ROLL": "roll", "BEE_GYRO_PITCH": "pitch", "BEE_GYRO_YAW": "yaw", "BEE_GYRO_RPY": "%2 get %1 (degrees)", "BEE_GYRO_SKAKING": "%1 is shaking?", "BEE_GYRO_REFRESH": "%1 refresh data", "BEE_OLED_DISPLAY": "%4 display %1 at x: %2 y: %3", "BEE_OLED_DRAW_PIXEL": "%3 draw pixel at x: %1 y: %2", "BEE_OLED_DRAW_LINE": "%5 draw line from x: %1 y: %2 to x: %3 y: %4", "BEE_OLED_DRAW_RECT": "%5 draw rectangle from x: %1 y: %2 with width %3 height %4", "BEE_OLED_DRAW_CIRCLE": "%4 draw circle at x: %1 y: %2 with radius %3", "BEE_OLED_DRAW_ICON": "%4 draw icon %1 at x: %2 y: %3", "BEE_OLED_DRAW_IMAGE": "%4 draw image %1 at x: %2 y: %3", "BEE_OLED_LOAD_BMP": "%2 load bmp %1", "BEE_OLED_CLEAR": "%1 clear", "BEE_OLED_RENDER": "%1 show", "BEE_BUZZER_NOTES": "%3 buzzer notes %1 duration %2", "BEE_BUZZER_NOTE": "%2 play note %1", "BEE_BUZZER_VOLUME": "%2 set volume %1 %", "BEE_BUZZER_QUIET": "%1 silent", "BEE_HIGH": "HIGH", "BEE_LOW": "LOW", "BEE_GPIO_OUTPUT": "%3 %2 digital write %1", "BEE_PWM_OUTPUT": "%3 %2 pwm write %1 %", "BEE_DC_MOTOR": "%3 %1 set speed to %2%", "BEE_STOP_MOTOR": "%2 %1 stop motor", "BEE_RUN_SERVO": "%3 %1 move %2 degrees", "BEE_STOP_SERVO": "%2 %1 stop servo", "BEE_MOVE_FORWARD": "%3 move forward with %1% power in %2 seconds", "BEE_MOVE_BACKWARD": "%3 move backward with %1% power in %2 seconds", "BEE_TURN_LEFT": "%3 turn left with %1% power in %2 seconds", "BEE_TURN_RIGHT": "%3 turn right with %1% power in %2 seconds", "BEE_STOP_ROBOT": "%1 stop robot", "BEE_SET_OTA": "%3 set OTA with Wifi %1 Password %2", "BEE_SET_SERIAL": "%1 set serial", "BEE_OTA_IP": "%2 set OTA IP %1", "BEE_RESET": "%1 reset board", "BEE_BLE_CONNECT": "%2 connect BLE with name %1", "BEE_BLE_DISCONNECT": "%1 disconnect", "BEE_BLE_READ": "%1 read", "BEE_BLE_SEND": "%2 send %1", "BEE_COLOR_SENSOR_READ": "%3 %2 get %1 value", "BEE_COLOR_SENSOR_CHECK_COLOR": "%3 %2 is %1?", "BEE_COLOR_SENSOR_IS_DETECTED_START_PROGRAM": "%3 when %1 detect %2", "BEE_DHT11_MEASURE": "%2 %1 measure", "BEE_DHT11_TEMPERATURE": "%2 %1 temperature (°C)", "BEE_DHT11_HUMIDITY": "%2 %1 humidity (%)", "BEE_GROVE_LIGHT_SENSOR_READ_RAW": "%2 %1 read raw value", "BEE_GROVE_LIGHT_SENSOR_READ_PERCENT": "%2 %1 read percentage", "BEE_GROVE_LIGHT_SENSOR_IS_BRIGHT": "%3 %2 is bright than %1%?", "BEE_GROVE_LIGHT_SENSOR_IS_DARK": "%3 %2 is dark than %1%?", "BEE_GROVE_ULTRASONIC_MEASURE": "%3 %2 get distance in %1", "BEE_GROVE_ULTRASONIC_COMPARE": "%4 %1 is %2 %3mm?", "BEE_HDC1080_TEMPERATURE": "%2 %1 temperature (°C)", "BEE_HDC1080_HUMIDITY": "%2 %1 humidity (%)", "BEE_LED7SEG_TEXT": "%3 %2 display %1", "BEE_LED7SEG_TEMPERATURE": "%3 %2 display %1 °C", "BEE_LED7SEG_CLEAR": "%2 %1 clear", "BEE_LEDBAR_SET_LEVEL": "%3 %2 set level to %1", "BEE_LEDBAR_SET_REVERSE": "%2 %1 set reverse", "BEE_LINE_SENSOR_READ": "%3 %2 get sensor %1 value", "BEE_MATRIX_DISPLAY_CUSTOM": "%4 %3 display %1 %2", "BEE_MATRIX_OFF": "%2 %1 turn off", "BEE_MATRIX_CLEAR": "%2 %1 clear", "BEE_MATRIX_SHOW": "%2 %1 show", "BEE_MATRIX_COLOR": "%3 %2 set color to %1", "BEE_MATRIX_TEXT": "%4 %3 display %1 delay %2 ms", "BEE_MATRIX_DIGIT": "%3 %2 display number %1", "BEE_MIC_SENSOR": "%2 %1 read sound value", "BEE_MIC_THRESHOLD": "%3 %2 if sound > %1%", "BEE_PIR_SENSOR_DETECT_MOTION": "%2 %1 is motion detected?", "BEE_PIR_SENSOR_WAIT_FOR_MOTION": "%3 %1 wait for motion in %2 seconds", "BEE_ULTRASONIC_MEASURE": "%3 %2 get distance in %1", "BEE_ULTRASONIC_COMPARE": "%4 %1 is %2 %3mm?", "BEE_WIFI_CONNECT": "%3 connect wifi %1 password %2", "BEE_WIFI_IS_CONNECTED": "%1 is connected?", "BEE_WIFI_IP_ADDRESS": "%1 get ip address", "BEE_WIFI_GAMEPAD_SERVER": "%1 start gamepad server on port %2", "BEE_WIFI_GAMEPAD_JOYSTICK": "%1 gamepad %2 stick %3 axis", "BEE_WIFI_GAMEPAD_BUTTON": "%1 gamepad button %2 is pressed", "BEE_WIFI_GAMEPAD_HANDLER": "%1", "BEE_HTTP_REQUEST": "%4 %1 link %2 with data %3", "BEE_HTTP_STATUS_CODE": "%1 get status code", "BEE_HTTP_STATUS_MESSAGE": "%1 get message", "BEE_HTTP_CLOSE": "%1 close connection", "BEE_MQTT_CONNECT": "%3 connect url %1 port %2", "BEE_MQTT_PUBLISH": "%3 publish %1 data %2", "BEE_MQTT_CONNECT_SERVER": "%1 connect server", "BEE_MQTT_SUBSCRIBE_TOPIC": "%2 subscribe topic %1", "BEE_MQTT_ON_RECEIVE": "%3 when receive %1 %2", "BEE_MQTT_CHECK_MSG": "%1 check message", "BEE_MQTT_GET_TOPIC": "%1 get receive topic", "BEE_MQTT_GET_MESSAGE": "%1 get receive message", "BEE_GROVE_ROTATION_SENSOR_READ_ANGLE_RAW": "%2 %1 read angle raw", "BEE_GROVE_ROTATION_SENSOR_READ_ANGLE_DEGREE": "%2 %1 read angle degree", "BEE_GROVE_ROTATION_SENSOR_READ_ANGLE_PERCENTAGE": "%2 %1 read angle percentage", "BEE_GROVE_ROTATION_SENSOR_IS_ROTATED_PAST": "%4 %1 is rotated past %2 %3", "BEE_GROVE_ROTATION_SENSOR_IS_ROTATED_BELOW": "%4 %1 is rotated below %2 %3", "BEE_RC522_SCAN_CARD": "%2 %1 scan card", "BEE_RC522_READ_TAG_ID": "%2 %1 read tag id", "BEE_RC522_SCAN_AND_ADD_CARD": "%2 %1 scan and add card", "BEE_RC522_SCAN_AND_CHECK": "%3 %1 scan and check card %2", "BEE_RC522_SCAN_AND_REMOVE_CARD": "%2 %1 scan and remove card", "BEE_RC522_CLEAR_LIST": "%1 clear list", "BEE_GAMEPAD_SETUP": "%1 setup gamepad", "BEE_GAMEPAD_READ_COMMAND": "%1 read gamepad command", "BEE_GAMEPAD_LAST_COMMAND": "%1 last gamepad command", "BEE_GAMEPAD_IS_PRESSED": "%2 gamepad button %1 is pressed", "BEE_GAMEPAD_WHEN_PRESSED": "%2 when gamepad button %1 pressed", "BEE_GAMEPAD_DIRECTION_PRESSED": "%2 gamepad direction %1 pressed", "BEE_GAMEPAD_ACTION_PRESSED": "%2 gamepad action %1 pressed"}