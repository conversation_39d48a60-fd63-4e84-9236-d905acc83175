{"START_PROGRAM": "%1 khi chư<PERSON><PERSON> trình b<PERSON>t đầu", "CONTROL_IF": "nếu %1 thì", "CONTROL_IF_ELSE": "<PERSON><PERSON><PERSON><PERSON> lại thì", "CONTROL_REPEAT": "lặp lại", "CONTROL_TIMES": "<PERSON><PERSON><PERSON>", "CONTROL_FOREVER": "lặp lại mãi mãi", "CONTROL_WHILE": "khi", "CONTROL_UNTIL": "chờ cho đến khi", "CONTROL_FOR": "lặp lại cho", "CONTROL_FROM": "từ", "CONTROL_TO": "<PERSON><PERSON><PERSON>", "CONTROL_BY": "b<PERSON><PERSON><PERSON>", "CONTROL_BREAK": "thoát khỏi vòng lặp", "CONTROL_CONTINUE": "t<PERSON><PERSON><PERSON> t<PERSON>c", "CONTROL_SET_RANGE": "đặt phạm vi", "CONTROL_LOW": "thấp nh<PERSON>t", "CONTROL_HIGH": "cao nh<PERSON>t", "CONTROL_WAIT": "chờ", "CONTROL_SECONDS": "giây", "PRINT": "in ra màn hình", "TEXT_JOIN": "<PERSON><PERSON><PERSON>", "BEE_LED_ON": "%5 bật %1 với màu đỏ %2 lục %3 xanh %4", "BEE_LED_OFF": "%2 tắt %1", "BEE_PRESS_BUTTON_START": "%3 khi nút %1 được %2", "BEE_BUTTON_PRESSED": "nhấn", "BEE_BUTTON_RELEASED": "thả", "BEE_BUTTON_LONG_PRESSED": "nhấn giữ", "BEE_BUTTON_STATUS": "%3 nút %1 đang %2?", "BEE_GPIO_READ": "%2 đọc digital %1", "BEE_ANALOG_READ": "%2 đọc analog %1", "BEE_GYRO_CALIBRATE": "%1 cân chỉnh", "BEE_GYRO_ROLL": "<PERSON><PERSON> nghiêng ngang", "BEE_GYRO_PITCH": "<PERSON><PERSON> nghi<PERSON> dọc", "BEE_GYRO_YAW": "<PERSON><PERSON> xoay", "BEE_GYRO_RPY": "%2 lấy %1 (độ)", "BEE_GYRO_SKAKING": "%1 đang lắc?", "BEE_GYRO_REFRESH": "%1 cập nhật dữ liệu", "BEE_OLED_DISPLAY": "%4 hiển thị %1 tại x: %2 y: %3", "BEE_OLED_DRAW_PIXEL": "%3 vẽ pixel tại x: %1 y: %2", "BEE_OLED_DRAW_LINE": "%5 vẽ đường thẳng từ x: %1 y: %2 đến x: %3 y: %4", "BEE_OLED_DRAW_RECT": "%5 vẽ hình chữ nhật từ x: %1 y: %2 với chiều rộng %3 và chiều cao %4", "BEE_OLED_DRAW_CIRCLE": "%4 vẽ hình tròn tại x: %1 y: %2 với b<PERSON> k<PERSON>h %3", "BEE_OLED_DRAW_ICON": "%4 vẽ icon %1 tại x: %2 y: %3", "BEE_OLED_DRAW_IMAGE": "%4 vẽ ảnh %1 tại x: %2 y: %3", "BEE_OLED_LOAD_BMP": "%2 t<PERSON><PERSON> bmp %1", "BEE_OLED_CLEAR": "%1 xóa", "BEE_OLED_RENDER": "%1 hiển thị", "BEE_BUZZER_NOTES": "%3 buzzer phát %1 quãng %2", "BEE_BUZZER_NOTE": "%2 phát nốt %1", "BEE_BUZZER_VOLUME": "%2 đặt âm lượng %1 %", "BEE_BUZZER_QUIET": "%1 im lặng", "BEE_HIGH": "<PERSON>", "BEE_LOW": "<PERSON><PERSON><PERSON><PERSON>", "BEE_GPIO_OUTPUT": "%3 %2 xuất digital mức %1", "BEE_PWM_OUTPUT": "%3 %2 xuất pwm mức %1 %", "BEE_DC_MOTOR": "%3 đặt %1 tốc độ %2%", "BEE_STOP_MOTOR": "%2 dừng motor %1", "BEE_RUN_SERVO": "%3 đặt %1 góc %2 độ", "BEE_STOP_SERVO": "%2 dừng servo %1", "BEE_MOVE_FORWARD": "%3 di chuyển tiến với %1% trong %2 giây", "BEE_MOVE_BACKWARD": "%3 di chuyển lùi với %1% trong %2 giây", "BEE_TURN_LEFT": "%3 xoay trái với %1% trong %2 giây", "BEE_TURN_RIGHT": "%3 xoay phải với %1% trong %2 giây", "BEE_STOP_ROBOT": "%1 dừng robot", "BEE_SET_OTA": "%3 đặt OTA với Wifi %1 mật khẩu %2", "BEE_SET_SERIAL": "%1 đặt serial", "BEE_OTA_IP": "%2 đặt IP OTA %1", "BEE_RESET": "%1 đặt lại board", "BEE_BLE_CONNECT": "%2 kết nối BLE với tên %1", "BEE_BLE_DISCONNECT": "%1 ngắt kết n<PERSON>i", "BEE_BLE_READ": "%1 đọc", "BEE_BLE_SEND": "%2 gửi %1", "BEE_COLOR_SENSOR_READ": "%3 %2 lấy giá trị màu %1", "BEE_COLOR_SENSOR_CHECK_COLOR": "%3 %2 là màu %1?", "BEE_COLOR_SENSOR_IS_DETECTED_START_PROGRAM": "%3 khi %1 phát hiện màu %2", "BEE_DHT11_MEASURE": "%2 %1 cập nhật dữ liệu", "BEE_DHT11_TEMPERATURE": "%2 %1 nhiệt độ (°C)", "BEE_DHT11_HUMIDITY": "%2 %1 độ ẩm (%)", "BEE_GROVE_LIGHT_SENSOR_READ_RAW": "%2 %1 đọc giá trị raw", "BEE_GROVE_LIGHT_SENSOR_READ_PERCENT": "%2 %1 đ<PERSON>c giá trị phần trăm", "BEE_GROVE_LIGHT_SENSOR_IS_BRIGHT": "%3 %2 sáng h<PERSON>n %1%?", "BEE_GROVE_LIGHT_SENSOR_IS_DARK": "%3 %2 t<PERSON>i hơn %1%?", "BEE_GROVE_ULTRASONIC_MEASURE": "%3 %2 đo k<PERSON><PERSON><PERSON> cách theo %1", "BEE_GROVE_ULTRASONIC_COMPARE": "%4 %1 có khoảng cách %2 %3mm?", "BEE_HDC1080_TEMPERATURE": "%2 %1 đo nhiệt độ (°C)", "BEE_HDC1080_HUMIDITY": "%2 %1 đo độ ẩm (%)", "BEE_LED7SEG_TEXT": "%3 %2 hiển thị %1", "BEE_LED7SEG_TEMPERATURE": "%3 %2 hiển thị nhiệt độ %1 °C", "BEE_LED7SEG_CLEAR": "%2 %1 xóa", "BEE_LEDBAR_SET_LEVEL": "%3 %2 đặt mức %1", "BEE_LEDBAR_SET_REVERSE": "%2 %1 đ<PERSON><PERSON>", "BEE_LINE_SENSOR_READ": "%3 %2 lấy giá trị cảm biến %1", "BEE_MATRIX_DISPLAY_CUSTOM": "%4 %3 hiển thị %1 với %2", "BEE_MATRIX_OFF": "%2 %1 tắt", "BEE_MATRIX_CLEAR": "%2 %1 xóa", "BEE_MATRIX_SHOW": "%2 %1 hiển thị", "BEE_MATRIX_COLOR": "%3 %2 đặt màu %1", "BEE_MATRIX_TEXT": "%4 %3 hiển thị %1 với độ trễ %2 ms", "BEE_MATRIX_DIGIT": "%3 %2 hiển thị số %1", "BEE_MIC_SENSOR": "%2 %1 đọc giá trị âm thanh", "BEE_MIC_THRESHOLD": "%3 %2 nếu âm thanh lớn hơn %1%", "BEE_PIR_SENSOR_DETECT_MOTION": "%2 %1 phát hiện chuyển động?", "BEE_PIR_SENSOR_WAIT_FOR_MOTION": "%3 %1 chờ chuyển động trong %2 giây", "BEE_ULTRASONIC_MEASURE": "%3 %2 đo k<PERSON><PERSON><PERSON> cách theo %1", "BEE_ULTRASONIC_COMPARE": "%4 %1 có khoảng cách %2 %3mm?", "BEE_WIFI_CONNECT": "%3 kết nối wifi %1 mật khẩu %2", "BEE_WIFI_IS_CONNECTED": "%1 đã kết nối?", "BEE_WIFI_IP_ADDRESS": "%1 lấy địa chỉ IP", "BEE_WIFI_GAMEPAD_SERVER": "%1 khởi động server gamepad cổng %2", "BEE_WIFI_GAMEPAD_JOYSTICK": "%1 gamepad cần %2 trục %3", "BEE_WIFI_GAMEPAD_BUTTON": "%1 gamepad nút %2 đ<PERSON><PERSON><PERSON> nhấn", "BEE_WIFI_GAMEPAD_HANDLER": "%1", "BEE_HTTP_REQUEST": "%4 %1 đường dẫn %2 với dữ liệu %3", "BEE_HTTP_STATUS_CODE": "%1 lấy trạng thái", "BEE_HTTP_STATUS_MESSAGE": "%1 l<PERSON>y kết quả", "BEE_HTTP_CLOSE": "%1 đóng kết n<PERSON>i", "BEE_MQTT_CONNECT": "%3 kết nối MQTT %1 cổng %2", "BEE_MQTT_PUBLISH": "%3 gửi %1 dữ liệu %2", "BEE_MQTT_CONNECT_SERVER": "%1 kết nối server", "BEE_MQTT_SUBSCRIBE_TOPIC": "%2 đ<PERSON>ng ký topic %1", "BEE_MQTT_ON_RECEIVE": "%3 khi nhận %1 %2", "BEE_MQTT_CHECK_MSG": "%1 kiểm tra tin nhắn", "BEE_MQTT_GET_TOPIC": "%1 lấy topic nhận", "BEE_MQTT_GET_MESSAGE": "%1 lấy tin nhắn nhận", "BEE_GROVE_ROTATION_SENSOR_READ_ANGLE_RAW": "%2 %1 đọc raw", "BEE_GROVE_ROTATION_SENSOR_READ_ANGLE_DEGREE": "%2 %1 đọc độ", "BEE_GROVE_ROTATION_SENSOR_READ_ANGLE_PERCENTAGE": "%2 %1 đ<PERSON>c phần trăm", "BEE_GROVE_ROTATION_SENSOR_IS_ROTATED_PAST": "%4 %2 xoay hơn %1 %3?", "BEE_GROVE_ROTATION_SENSOR_IS_ROTATED_BELOW": "%4 %2 xoay dưới %1 %3?", "BEE_RC522_SCAN_CARD": "%2 %1 đọc thẻ", "BEE_RC522_READ_TAG_ID": "%2 %1 đọc ID thẻ", "BEE_RC522_SCAN_AND_ADD_CARD": "%2 %1 quét và thêm thẻ", "BEE_RC522_SCAN_AND_CHECK": "%2 %1 quét và kiểm tra thẻ", "BEE_RC522_SCAN_AND_REMOVE_CARD": "%2 %1 quét và xóa thẻ", "BEE_RC522_CLEAR_LIST": "%2 %1 xóa danh sách thẻ", "BEE_GAMEPAD_SETUP": "%1 thiết lập gamepad", "BEE_GAMEPAD_READ_COMMAND": "%1 đ<PERSON><PERSON> l<PERSON> gamepad", "BEE_GAMEPAD_LAST_COMMAND": "%1 lệnh gamepad cuối", "BEE_GAMEPAD_IS_PRESSED": "%2 nút gamepad %1 đ<PERSON><PERSON><PERSON> n<PERSON>n", "BEE_GAMEPAD_WHEN_PRESSED": "%2 khi nút gamepad %1 đ<PERSON><PERSON><PERSON> n<PERSON>n", "BEE_GAMEPAD_DIRECTION_PRESSED": "%2 hướng gamepad %1 đ<PERSON><PERSON><PERSON> n<PERSON>n", "BEE_GAMEPAD_ACTION_PRESSED": "%2 hành động gamepad %1 đ<PERSON><PERSON><PERSON> n<PERSON>ấn"}