import * as Blockly from "blockly";
import { pythonGenerator } from "blockly/python";

import GamepadIcon from "./images/gamepad.png";

// ========== GAMEPAD SETUP ========== //

// Setup gamepad block
Blockly.Blocks["gamepad_setup"] = {
    init: function () {
        this.jsonInit({
            type: "gamepad_setup",
            message0: "%{BKY_BEE_GAMEPAD_SETUP}", // %1 setup gamepad
            args0: [
                {
                    type: "field_image",
                    src: GamepadIcon,
                    width: 45,
                    height: 45,
                    alt: "gamepad",
                },
            ],
            previousStatement: null,
            nextStatement: null,
            colour: "#9C27B0",
            tooltip: "Setup gamepad to receive commands from web interface",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["gamepad_setup"] = function (block) {
    var code = `# == Gamepad Setup ==
import sys
import time

class Gamepad:
    def __init__(self):
        self.last_command = ""
        self.commands = {
            "up": False,
            "down": False,
            "left": False,
            "right": False,
            "center": False,
            "a": False,
            "b": False,
            "x": False,
            "y": False,
            "start": False,
            "select": False,
            "stop": False
        }
        self.last_check_time = 0
        self.check_interval = 0.01  # Check every 10ms

    def read_command(self):
        # Non-blocking check with time interval to prevent REPL blocking
        current_time = time.ticks_ms()
        if time.ticks_diff(current_time, self.last_check_time) < self.check_interval * 1000:
            return ""

        self.last_check_time = current_time

        try:
            # For MicroPython - use non-blocking approach
            import select
            if hasattr(sys.stdin, 'read'):
                # Try to read without blocking
                try:
                    # Check if data is available
                    if hasattr(select, 'poll'):
                        poll = select.poll()
                        poll.register(sys.stdin, select.POLLIN)
                        if poll.poll(0):  # 0 = non-blocking
                            line = sys.stdin.readline().strip()
                            return self._process_command(line)
                    else:
                        # Fallback for systems without poll
                        if sys.stdin in select.select([sys.stdin], [], [], 0)[0]:
                            line = sys.stdin.readline().strip()
                            return self._process_command(line)
                except:
                    pass
        except:
            pass
        return ""

    def _process_command(self, line):
        if line and line.startswith("gamepad."):
            command = line.replace("gamepad.", "").lower()
            self.last_command = command
            # Reset all commands
            for key in self.commands:
                self.commands[key] = False
            # Set current command
            if command in self.commands:
                self.commands[command] = True
            print(f"Gamepad: {command}")  # Debug output
            return command
        return ""

    def is_pressed(self, button):
        return self.commands.get(button.lower(), False)

    def get_last_command(self):
        return self.last_command

gamepad = Gamepad()

`;
    return code;
};

// ========== GAMEPAD READ COMMANDS ========== //

// Read gamepad command block
Blockly.Blocks["gamepad_read_command"] = {
    init: function () {
        this.jsonInit({
            type: "gamepad_read_command",
            message0: "%{BKY_BEE_GAMEPAD_READ_COMMAND}", // %1 read gamepad command
            args0: [
                {
                    type: "field_image",
                    src: GamepadIcon,
                    width: 45,
                    height: 45,
                    alt: "gamepad",
                },
            ],
            output: "String",
            colour: "#9C27B0",
            tooltip: "Read the latest gamepad command",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["gamepad_read_command"] = function (block) {
    var code = "gamepad.read_command()";
    return [code, pythonGenerator.ORDER_NONE];
};

// Get last command block
Blockly.Blocks["gamepad_last_command"] = {
    init: function () {
        this.jsonInit({
            type: "gamepad_last_command",
            message0: "%{BKY_BEE_GAMEPAD_LAST_COMMAND}", // %1 last gamepad command
            args0: [
                {
                    type: "field_image",
                    src: GamepadIcon,
                    width: 45,
                    height: 45,
                    alt: "gamepad",
                },
            ],
            output: "String",
            colour: "#9C27B0",
            tooltip: "Get the last gamepad command received",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["gamepad_last_command"] = function (block) {
    var code = "gamepad.get_last_command()";
    return [code, pythonGenerator.ORDER_NONE];
};

// ========== GAMEPAD BUTTON CHECKS ========== //

// Check if button is pressed
Blockly.Blocks["gamepad_is_pressed"] = {
    init: function () {
        this.jsonInit({
            type: "gamepad_is_pressed",
            message0: "%{BKY_BEE_GAMEPAD_IS_PRESSED}", // %2 gamepad button %1 is pressed
            args0: [
                {
                    type: "field_dropdown",
                    name: "BUTTON",
                    options: [
                        ["↑ UP", "up"],
                        ["↓ DOWN", "down"],
                        ["← LEFT", "left"],
                        ["→ RIGHT", "right"],
                        ["● CENTER", "center"],
                        ["A", "a"],
                        ["B", "b"],
                        ["X", "x"],
                        ["Y", "y"],
                        ["START", "start"],
                        ["SELECT", "select"],
                        ["STOP", "stop"],
                    ],
                },
                {
                    type: "field_image",
                    src: GamepadIcon,
                    width: 45,
                    height: 45,
                    alt: "gamepad",
                },
            ],
            output: "Boolean",
            colour: "#9C27B0",
            tooltip: "Check if a specific gamepad button is currently pressed",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["gamepad_is_pressed"] = function (block) {
    var button = block.getFieldValue("BUTTON");
    var code = `gamepad.is_pressed("${button}")`;
    return [code, pythonGenerator.ORDER_NONE];
};

// ========== GAMEPAD EVENTS ========== //

// When button pressed event
Blockly.Blocks["gamepad_when_pressed"] = {
    init: function () {
        this.jsonInit({
            type: "gamepad_when_pressed",
            message0: "%{BKY_BEE_GAMEPAD_WHEN_PRESSED}", // %2 when gamepad button %1 pressed
            args0: [
                {
                    type: "field_dropdown",
                    name: "BUTTON",
                    options: [
                        ["↑ UP", "up"],
                        ["↓ DOWN", "down"],
                        ["← LEFT", "left"],
                        ["→ RIGHT", "right"],
                        ["● CENTER", "center"],
                        ["A", "a"],
                        ["B", "b"],
                        ["X", "x"],
                        ["Y", "y"],
                        ["START", "start"],
                        ["SELECT", "select"],
                        ["STOP", "stop"],
                    ],
                },
                {
                    type: "field_image",
                    src: GamepadIcon,
                    width: 45,
                    height: 45,
                    alt: "gamepad",
                },
            ],
            message1: "%1",
            args1: [
                {
                    type: "input_statement",
                    name: "DO",
                },
            ],
            previousStatement: null,
            nextStatement: null,
            colour: "#9C27B0",
            tooltip: "Execute code when a specific gamepad button is pressed",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["gamepad_when_pressed"] = function (block) {
    var button = block.getFieldValue("BUTTON");
    var statements_do = pythonGenerator.statementToCode(block, "DO");

    var code = `if gamepad.read_command() == "${button}":
${statements_do}`;
    return code;
};

// ========== GAMEPAD DIRECTION HELPERS ========== //

// Check direction pressed
Blockly.Blocks["gamepad_direction_pressed"] = {
    init: function () {
        this.jsonInit({
            type: "gamepad_direction_pressed",
            message0: "%{BKY_BEE_GAMEPAD_DIRECTION_PRESSED}", // %2 gamepad direction %1 pressed
            args0: [
                {
                    type: "field_dropdown",
                    name: "DIRECTION",
                    options: [
                        ["↑ UP", "up"],
                        ["↓ DOWN", "down"],
                        ["← LEFT", "left"],
                        ["→ RIGHT", "right"],
                    ],
                },
                {
                    type: "field_image",
                    src: GamepadIcon,
                    width: 45,
                    height: 45,
                    alt: "gamepad",
                },
            ],
            output: "Boolean",
            colour: "#9C27B0",
            tooltip: "Check if a direction button is pressed",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["gamepad_direction_pressed"] = function (block) {
    var direction = block.getFieldValue("DIRECTION");
    var code = `gamepad.is_pressed("${direction}")`;
    return [code, pythonGenerator.ORDER_NONE];
};

// Check action button pressed
Blockly.Blocks["gamepad_action_pressed"] = {
    init: function () {
        this.jsonInit({
            type: "gamepad_action_pressed",
            message0: "%{BKY_BEE_GAMEPAD_ACTION_PRESSED}", // %2 gamepad action %1 pressed
            args0: [
                {
                    type: "field_dropdown",
                    name: "ACTION",
                    options: [
                        ["A", "a"],
                        ["B", "b"],
                        ["X", "x"],
                        ["Y", "y"],
                    ],
                },
                {
                    type: "field_image",
                    src: GamepadIcon,
                    width: 45,
                    height: 45,
                    alt: "gamepad",
                },
            ],
            output: "Boolean",
            colour: "#9C27B0",
            tooltip: "Check if an action button is pressed",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["gamepad_action_pressed"] = function (block) {
    var action = block.getFieldValue("ACTION");
    var code = `gamepad.is_pressed("${action}")`;
    return [code, pythonGenerator.ORDER_NONE];
};

// ========== GAMEPAD SAFE LOOP ========== //

// Safe gamepad loop that doesn't block REPL
Blockly.Blocks["gamepad_safe_loop"] = {
    init: function () {
        this.jsonInit({
            type: "gamepad_safe_loop",
            message0: "%{BKY_BEE_GAMEPAD_SAFE_LOOP}", // %1 gamepad safe loop
            args0: [
                {
                    type: "field_image",
                    src: GamepadIcon,
                    width: 45,
                    height: 45,
                    alt: "gamepad",
                },
            ],
            message1: "%1",
            args1: [
                {
                    type: "input_statement",
                    name: "DO",
                },
            ],
            previousStatement: null,
            nextStatement: null,
            colour: "#9C27B0",
            tooltip: "Safe loop for gamepad that allows REPL interruption",
            helpUrl: "",
        });
    },
};

pythonGenerator.forBlock["gamepad_safe_loop"] = function (block) {
    var statements_do = pythonGenerator.statementToCode(block, "DO");

    var code = `# Safe gamepad loop - allows REPL interruption
try:
    while True:
        # Check for gamepad input
        gamepad.read_command()

        # Execute user code
${statements_do}

        # Small delay to prevent blocking REPL
        time.sleep_ms(10)

        # Allow KeyboardInterrupt to break the loop
        if gamepad.last_command == "stop":
            print("Gamepad loop stopped")
            break

except KeyboardInterrupt:
    print("Gamepad loop interrupted")
    pass
`;
    return code;
};

// Export all blocks
export const GamepadBlocks = {
    gamepad_setup: Blockly.Blocks["gamepad_setup"],
    gamepad_read_command: Blockly.Blocks["gamepad_read_command"],
    gamepad_last_command: Blockly.Blocks["gamepad_last_command"],
    gamepad_is_pressed: Blockly.Blocks["gamepad_is_pressed"],
    gamepad_when_pressed: Blockly.Blocks["gamepad_when_pressed"],
    gamepad_direction_pressed: Blockly.Blocks["gamepad_direction_pressed"],
    gamepad_action_pressed: Blockly.Blocks["gamepad_action_pressed"],
    gamepad_safe_loop: Blockly.Blocks["gamepad_safe_loop"],
};

// Export generators
export const GamepadGenerators = {
    gamepad_setup: pythonGenerator.forBlock["gamepad_setup"],
    gamepad_read_command: pythonGenerator.forBlock["gamepad_read_command"],
    gamepad_last_command: pythonGenerator.forBlock["gamepad_last_command"],
    gamepad_is_pressed: pythonGenerator.forBlock["gamepad_is_pressed"],
    gamepad_when_pressed: pythonGenerator.forBlock["gamepad_when_pressed"],
    gamepad_direction_pressed: pythonGenerator.forBlock["gamepad_direction_pressed"],
    gamepad_action_pressed: pythonGenerator.forBlock["gamepad_action_pressed"],
    gamepad_safe_loop: pythonGenerator.forBlock["gamepad_safe_loop"],
};
