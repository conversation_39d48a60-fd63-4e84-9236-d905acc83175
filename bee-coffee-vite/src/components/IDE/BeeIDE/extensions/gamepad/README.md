# BeE Gamepad Extension

This extension allows you to control your BeE board using a web-based gamepad interface.

## Features

### 🎮 Gamepad Blocks Available:

1. **Setup Block**

    - `gamepad setup` - Initialize the gamepad system

2. **Read Commands**

    - `read gamepad command` - Read the latest gamepad command
    - `last gamepad command` - Get the last command received

3. **Button Status**

    - `gamepad button [button] is pressed` - Check if a specific button is pressed
    - `gamepad direction [direction] pressed` - Check direction buttons (UP, DOWN, LEFT, RIGHT)
    - `gamepad action [action] pressed` - Check action buttons (A, B, X, Y)

4. **Events**

    - `when gamepad button [button] pressed` - Execute code when a button is pressed

5. **Safe Loop (Recommended)**
    - `gamepad safe loop` - Safe loop that doesn't block REPL, allows code re-upload

### 🎯 Supported Buttons:

**Direction Pad:**

-   ↑ UP
-   ↓ DOWN
-   ← LEFT
-   → RIGHT
-   ● CENTER

**Action Buttons:**

-   A, B, X, Y

**Control Buttons:**

-   START, SELECT, STOP

## How to Use

### 1. Setup Your Program

1. Open BeE IDE at `http://localhost:5173/play/bee-ide`
2. Add the "Gamepad Controller" extension
3. Start with the "gamepad setup" block
4. Add your gamepad control logic

### 2. Example Program (Recommended - Safe Loop)

```
when program start
├── gamepad setup
└── gamepad safe loop
    ├── when gamepad button UP pressed
    │   └── print "Moving UP!"
    ├── when gamepad button A pressed
    │   └── LED on with red 255 green 0 blue 0
    └── when gamepad button STOP pressed
        └── print "Gamepad stopped!"
```

**⚠️ Important:** Use `gamepad safe loop` instead of `forever` to avoid blocking REPL!

### 3. Use the Web Gamepad

1. Open the gamepad interface at `http://localhost:5173/play/gamepad`
2. Connect to your BeE board via USB
3. Upload your program to the BeE board
4. Use the gamepad to control your BeE board!

## Technical Details

### Command Format

The gamepad sends commands in the format: `gamepad.[command]`

Examples:

-   `gamepad.up` - UP button pressed
-   `gamepad.a` - A button pressed
-   `gamepad.stop` - STOP button pressed

### Python Code Generated

The extension generates MicroPython code that:

-   Creates a Gamepad class to handle input
-   Provides non-blocking command reading
-   Maintains button state tracking
-   Works with both regular Python and MicroPython

### Integration

-   Works seamlessly with other BeE board extensions
-   Compatible with LED, sensor, and communication blocks
-   Supports real-time control and feedback

## Tips

1. **Always start with `gamepad setup`** - This initializes the gamepad system
2. **Use `gamepad safe loop` instead of `forever`** - Prevents REPL blocking, allows code re-upload
3. **Use STOP button to exit loop** - Press STOP on gamepad to safely exit the loop
4. **Combine with other blocks** - Control LEDs, motors, sensors based on gamepad input
5. **Test with web gamepad** - Use the `/play/gamepad` interface for testing

## Troubleshooting

1. **Gamepad not responding:**

    - Make sure you added the "gamepad setup" block
    - Check if BeE board is connected via USB
    - Verify the program is uploaded and running

2. **Commands not recognized:**

    - Ensure you're using the correct button names
    - Check the web gamepad is connected to the same BeE board

3. **Cannot upload new code (REPL blocked):**

    - **Use `gamepad safe loop` instead of `forever`** - This is the main solution!
    - Press STOP button on gamepad to exit the loop
    - Use Ctrl+C in terminal to interrupt if needed
    - Restart BeE board if completely stuck

4. **Intermittent response:**
    - Use the gamepad blocks inside a safe loop
    - Make sure the program doesn't have other blocking operations

## Example Projects

1. **LED Controller** - Change LED colors with gamepad buttons
2. **Robot Control** - Control a robot's movement with direction pad
3. **Game Controller** - Create simple games controlled by gamepad
4. **Interactive Display** - Show different content based on button presses
