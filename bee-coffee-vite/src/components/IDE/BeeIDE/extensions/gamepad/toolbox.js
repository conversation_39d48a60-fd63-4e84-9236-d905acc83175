export default {
    id: "gamepad",
    kind: "category",
    name: "Gamepad",
    colour: "#9C27B0",
    contents: [
        // ========== SETUP ========== //
        {
            kind: "label",
            text: "Setup",
        },
        {
            kind: "block",
            type: "gamepad_setup",
        },

        // ========== READ COMMANDS ========== //
        {
            kind: "label",
            text: "Read Commands",
        },
        {
            kind: "block",
            type: "gamepad_read_command",
        },
        {
            kind: "block",
            type: "gamepad_last_command",
        },

        // ========== BUTTON CHECKS ========== //
        {
            kind: "label",
            text: "Button Status",
        },
        {
            kind: "block",
            type: "gamepad_is_pressed",
            blockxml: `
                <block type="gamepad_is_pressed">
                    <field name="BUTTON">up</field>
                </block>`,
        },
        {
            kind: "block",
            type: "gamepad_direction_pressed",
            blockxml: `
                <block type="gamepad_direction_pressed">
                    <field name="DIRECTION">up</field>
                </block>`,
        },
        {
            kind: "block",
            type: "gamepad_action_pressed",
            blockxml: `
                <block type="gamepad_action_pressed">
                    <field name="ACTION">a</field>
                </block>`,
        },

        // ========== EVENTS ========== //
        {
            kind: "label",
            text: "Events",
        },
        {
            kind: "block",
            type: "gamepad_when_pressed",
            blockxml: `
                <block type="gamepad_when_pressed">
                    <field name="BUTTON">up</field>
                </block>`,
        },

        // ========== SAFE LOOP ========== //
        {
            kind: "label",
            text: "Safe Loop (Recommended)",
        },
        {
            kind: "block",
            type: "gamepad_safe_loop",
        },

        // ========== EXAMPLES ========== //
        {
            kind: "label",
            text: "Examples",
        },
        {
            kind: "block",
            type: "gamepad_when_pressed",
            blockxml: `
                <block type="gamepad_when_pressed">
                    <field name="BUTTON">up</field>
                    <statement name="DO">
                        <block type="text_print">
                            <value name="TEXT">
                                <shadow type="text">
                                    <field name="TEXT">Moving UP!</field>
                                </shadow>
                            </value>
                        </block>
                    </statement>
                </block>`,
        },
        {
            kind: "block",
            type: "gamepad_when_pressed",
            blockxml: `
                <block type="gamepad_when_pressed">
                    <field name="BUTTON">a</field>
                    <statement name="DO">
                        <block type="beeboard_onboard_led_on">
                            <value name="red">
                                <shadow type="math_number">
                                    <field name="NUM">255</field>
                                </shadow>
                            </value>
                            <value name="green">
                                <shadow type="math_number">
                                    <field name="NUM">0</field>
                                </shadow>
                            </value>
                            <value name="blue">
                                <shadow type="math_number">
                                    <field name="NUM">0</field>
                                </shadow>
                            </value>
                            <value name="led_number">
                                <shadow type="math_number">
                                    <field name="NUM">2</field>
                                </shadow>
                            </value>
                        </block>
                    </statement>
                </block>`,
        },

        // ========== COMPLETE EXAMPLE ========== //
        {
            kind: "label",
            text: "Complete Example (Safe)",
        },
        {
            kind: "block",
            type: "start_program",
            blockxml: `
                <block type="start_program">
                    <next>
                        <block type="gamepad_setup">
                            <next>
                                <block type="gamepad_safe_loop">
                                    <statement name="DO">
                                        <block type="gamepad_when_pressed">
                                            <field name="BUTTON">up</field>
                                            <statement name="DO">
                                                <block type="text_print">
                                                    <value name="TEXT">
                                                        <shadow type="text">
                                                            <field name="TEXT">UP pressed!</field>
                                                        </shadow>
                                                    </value>
                                                </block>
                                            </statement>
                                            <next>
                                                <block type="gamepad_when_pressed">
                                                    <field name="BUTTON">a</field>
                                                    <statement name="DO">
                                                        <block type="beeboard_onboard_led_on">
                                                            <value name="red">
                                                                <shadow type="math_number">
                                                                    <field name="NUM">255</field>
                                                                </shadow>
                                                            </value>
                                                            <value name="green">
                                                                <shadow type="math_number">
                                                                    <field name="NUM">0</field>
                                                                </shadow>
                                                            </value>
                                                            <value name="blue">
                                                                <shadow type="math_number">
                                                                    <field name="NUM">0</field>
                                                                </shadow>
                                                            </value>
                                                            <value name="led_number">
                                                                <shadow type="math_number">
                                                                    <field name="NUM">2</field>
                                                                </shadow>
                                                            </value>
                                                        </block>
                                                    </statement>
                                                    <next>
                                                        <block type="gamepad_when_pressed">
                                                            <field name="BUTTON">stop</field>
                                                            <statement name="DO">
                                                                <block type="text_print">
                                                                    <value name="TEXT">
                                                                        <shadow type="text">
                                                                            <field name="TEXT">Gamepad stopped!</field>
                                                                        </shadow>
                                                                    </value>
                                                                </block>
                                                            </statement>
                                                        </block>
                                                    </next>
                                                </block>
                                            </next>
                                        </block>
                                    </statement>
                                </block>
                            </next>
                        </block>
                    </next>
                </block>`,
        },
    ],
};
