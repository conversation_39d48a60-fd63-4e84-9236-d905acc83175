import { BeeBoardBlocks, BeeBoardGenerators } from "./beeboard/blocks";
import { WifiBlocks, WifiGenerators } from "./wifi/blocks";
import { BleBlocks, BleGenerators } from "./ble/blocks";
import { MqttBlocks, MqttGenerators } from "./mqtt/blocks";
import { HttpBlocks, HttpGenerators } from "./http/blocks";
import { Led7SegmentBlocks, Led7SegmentGenerators } from "./led7seg/blocks";
import { UltrasonicBlocks, UltrasonicGenerators } from "./ultrasonic/blocks";
import { ColorSensorBlocks, ColorSensorGenerators } from "./color_sensor/blocks";
import { MicBlocks, MicsGenerators } from "./mic/blocks";
import { LineSensorBlocks, LineSensorGenerators } from "./line_sensor/blocks";
import { HDC1080Blocks, HDC1080Generators } from "./hdc1080/blocks";
import { MatrixBlocks, MatrixGenerators } from "./matrix/blocks";
import { DHT11Blocks, DHT11Generators } from "./dht11/blocks";
import { LedBarBlocks, LedBarGenerators } from "./ledbar/blocks";
import { GroveUltrasonicBlocks, GroveUltrasonicGenerators } from "./grove_ultrasonic/blocks";
import { GroveLightSensorBlocks, GroveLightSensorGenerators } from "./grove_light_sensor/blocks";
import { PirSensorBlocks, PirSensorGenerators } from "./pir_sensor/blocks";
import { GroveRotationSensorBlocks, GroveRotationSensorGenerators } from "./grove_rotation_sensor/blocks";
import { RC522Blocks, RC522Generators } from "./rc522/blocks";
import { GamepadBlocks, GamepadGenerators } from "./gamepad/blocks";

import BeeBoardImage from "./beeboard/images/BeeBoard.png";
import WifiImage from "./wifi/images/wifi.svg";
import BleImage from "./ble/images/bleIcon.png";
import MqttImage from "./mqtt/images/mqttIcon.png";
import HttpImage from "./http/images/httpIcon.png";
import Led7SegmentImage from "./led7seg/images/ledAvatar.webp";
import UltrasonicImage from "./ultrasonic/images/ultrasonicAvatar.png";
import ColorSensorImage from "./color_sensor/images/colorsensorAvatar.png";
import MicImage from "./mic/images/micAvatar.png";
import LineSensorImage from "./line_sensor/images/lineSensorAvatar.jpg";
import HDC1080Image from "./hdc1080/images/hdc1080Avatar.png";
import MatrixImage from "./matrix/images/matrixAvatar.png";
import DHT11Image from "./dht11/images/dht11Avatar.png";
import LedBarImage from "./ledbar/images/ledbarAvatar.webp";
import GroveUltrasonicImage from "./grove_ultrasonic/images/groveUltrasonicAvatar.webp";
import GroveLightSensorImage from "./grove_light_sensor/images/lightSensorAvatar.webp";
import PirSensorImage from "./pir_sensor/images/pirAvatar.png";
import GroveRotationSensorImage from "./grove_rotation_sensor/images/rotationAvatar.webp";
import RC522Image from "./rc522/images/rc522Avatar.webp";
import GamepadImage from "./gamepad/images/gamepad.png";

// Extension registry
export const EXTENSIONS = [
    {
        id: "beeboard",
        name: "BeE board",
        description: "Control your BeE board",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#1a237e",
        image: BeeBoardImage,
        category: "Board",
        blocks: BeeBoardBlocks,
        generators: BeeBoardGenerators,
    },
    {
        id: "wifi",
        name: "Wifi",
        description: "Connect to Wifi",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#f500ab",
        image: WifiImage,
        category: "Network",
        blocks: WifiBlocks,
        generators: WifiGenerators,
    },
    {
        id: "ble",
        name: "BLE",
        description: "Connect to Bluetooth",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#4763ff",
        image: BleImage,
        category: "Network",
        blocks: BleBlocks,
        generators: BleGenerators,
    },
    {
        id: "mqtt",
        name: "MQTT",
        description: "Connect to MQTT",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#9400ab",
        image: MqttImage,
        category: "Network",
        blocks: MqttBlocks,
        generators: MqttGenerators,
    },
    {
        id: "http",
        name: "HTTP",
        description: "Connect to HTTP",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#00b850",
        image: HttpImage,
        category: "Network",
        blocks: HttpBlocks,
        generators: HttpGenerators,
    },
    {
        id: "led7seg",
        name: "Led 7 Segments",
        description: "Display number and text",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#d47100",
        image: Led7SegmentImage,
        category: "Display",
        blocks: Led7SegmentBlocks,
        generators: Led7SegmentGenerators,
    },
    {
        id: "ultrasonic",
        name: "Ultrasonic",
        description: "Measure the distance",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#00c925",
        image: UltrasonicImage,
        category: "Sensor",
        blocks: UltrasonicBlocks,
        generators: UltrasonicGenerators,
    },
    {
        id: "grove_ultrasonic",
        name: "Grove Ultrasonic",
        description: "Measure the distance with Grove Ultrasonic v2.0",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#a400ba",
        image: GroveUltrasonicImage,
        category: "Sensor",
        blocks: GroveUltrasonicBlocks,
        generators: GroveUltrasonicGenerators,
        modules: ["BeeGroveUltrasonic.py"],
    },
    {
        id: "color_sensor",
        name: "Color",
        description: "Detect color",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#bcbf00",
        image: ColorSensorImage,
        category: "Sensor",
        blocks: ColorSensorBlocks,
        generators: ColorSensorGenerators,
    },
    {
        id: "mic",
        name: "Microphone",
        description: "Hear the sound",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#6800f0",
        image: MicImage,
        category: "Sensor",
        blocks: MicBlocks,
        generators: MicsGenerators,
        modules: ["BeeMic.py"],
    },
    {
        id: "line_sensor",
        name: "Line Sensor",
        description: "Detect line",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#690300",
        image: LineSensorImage,
        category: "Sensor",
        blocks: LineSensorBlocks,
        generators: LineSensorGenerators,
    },
    {
        id: "hdc1080",
        name: "HC1080",
        description: "Measure humidity and temperature with HDC1080",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#0096d6",
        image: HDC1080Image,
        category: "Sensor",
        blocks: HDC1080Blocks,
        generators: HDC1080Generators,
        modules: ["BeeHDC1080.py"],
    },
    {
        id: "matrix",
        name: "Led Matrix",
        description: "Display led matrix",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#005873",
        image: MatrixImage,
        category: "Display",
        blocks: MatrixBlocks,
        generators: MatrixGenerators,
    },
    {
        id: "dht11",
        name: "DHT11",
        description: "Measure humidity and temperature with DHT11",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#0096d6",
        image: DHT11Image,
        category: "Sensor",
        blocks: DHT11Blocks,
        generators: DHT11Generators,
    },
    {
        id: "ledbar",
        name: "Led Bar",
        description: "Display level with led bar",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#ff9914",
        image: LedBarImage,
        category: "Display",
        blocks: LedBarBlocks,
        generators: LedBarGenerators,
        modules: ["BeeMY9221.py"],
    },
    {
        id: "grove_light_sensor",
        name: "Grove Light Sensor",
        description: "Measure light intensity with Grove Light Sensor",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#690300",
        image: GroveLightSensorImage,
        category: "Sensor",
        blocks: GroveLightSensorBlocks,
        generators: GroveLightSensorGenerators,
        modules: ["BeeGroveLightSensor.py"],
    },
    {
        id: "pir_sensor",
        name: "PIR Sensor",
        description: "Detect motion with PIR Sensor",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#039dfc",
        image: PirSensorImage,
        category: "Sensor",
        blocks: PirSensorBlocks,
        generators: PirSensorGenerators,
        modules: ["BeePirSensor.py"],
    },
    {
        id: "grove_rotation_sensor",
        name: "Grove Rotation Sensor",
        description: "Measure rotation with Grove Rotation Sensor",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#007e8c",
        image: GroveRotationSensorImage,
        category: "Sensor",
        blocks: GroveRotationSensorBlocks,
        generators: GroveRotationSensorGenerators,
        modules: ["BeeGroveRotationSensor.py"],
    },
    {
        id: "rc522",
        name: "RFID Reader",
        description: "Read RFID with RC522",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#007e8c",
        image: RC522Image,
        category: "Sensor",
        blocks: RC522Blocks,
        generators: RC522Generators,
    },
    {
        id: "gamepad",
        name: "Gamepad Controller",
        description: "Control BeE board with web gamepad",
        author: "BeE STEM Solutions",
        version: "1.0.0",
        color: "#9C27B0",
        image: GamepadImage,
        category: "Input",
        blocks: GamepadBlocks,
        generators: GamepadGenerators,
    },
    // Add other extensions here
];
