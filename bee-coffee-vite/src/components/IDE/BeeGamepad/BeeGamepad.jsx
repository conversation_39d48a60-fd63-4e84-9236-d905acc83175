import React, { useState, useEffect, useRef } from "react";
import {
    Box,
    Container,
    Typography,
    Button,
    Paper,
    Grid,
    IconButton,
    Tooltip,
    Alert,
    Snackbar,
    Card,
    CardContent,
    Switch,
    FormControlLabel,
} from "@mui/material";
import {
    <PERSON>Up<PERSON>,
    ArrowDownward,
    ArrowBack,
    ArrowForward,
    RadioButtonUnchecked,
    Stop,
    PlayArrow,
    Pause,
} from "@mui/icons-material";
import BeeAppBar from "./BeeAppBar";
import { serialConnection } from "../Common/Connection";
import { useDocumentTitle } from "../../../hooks/useDocumentTitle";

function BeeGamepad() {
    useDocumentTitle("BeE Gamepad Controller");

    const [project, setProject] = useState({
        serialConnected: false,
        port: null,
        writer: null,
        reader: null,
    });

    const [message, setMessage] = useState("");
    const [snackbarOpen, setSnackbarOpen] = useState(false);
    const [isPressed, setIsPressed] = useState({});
    const [continuousMode, setContinuousMode] = useState(false);
    const intervalRef = useRef(null);

    // Gamepad button configurations
    const gamepadButtons = [
        { id: "up", label: "↑", command: "UP", icon: ArrowUpward, position: { row: 0, col: 1 } },
        { id: "left", label: "←", command: "LEFT", icon: ArrowBack, position: { row: 1, col: 0 } },
        { id: "center", label: "●", command: "CENTER", icon: RadioButtonUnchecked, position: { row: 1, col: 1 } },
        { id: "right", label: "→", command: "RIGHT", icon: ArrowForward, position: { row: 1, col: 2 } },
        { id: "down", label: "↓", command: "DOWN", icon: ArrowDownward, position: { row: 2, col: 1 } },
    ];

    const actionButtons = [
        { id: "a", label: "A", command: "A", color: "success" },
        { id: "b", label: "B", command: "B", color: "error" },
        { id: "x", label: "X", command: "X", color: "info" },
        { id: "y", label: "Y", command: "Y", color: "warning" },
    ];

    const controlButtons = [
        { id: "start", label: "START", command: "START", icon: PlayArrow },
        { id: "select", label: "SELECT", command: "SELECT", icon: Pause },
        { id: "stop", label: "STOP", command: "STOP", icon: Stop },
    ];

    // Handle serial connection
    const handleConnectSerial = async () => {
        const result = await serialConnection.connect();
        
        setProject((prev) => ({
            ...prev,
            serialConnected: result.connected,
            port: serialConnection.port,
            writer: serialConnection.writer,
            reader: serialConnection.reader,
        }));

        setMessage(result.message);
        setSnackbarOpen(true);
    };

    // Send command to BeE board
    const sendCommand = async (command) => {
        if (!project.serialConnected || !project.writer) {
            setMessage("Please connect to BeE board first!");
            setSnackbarOpen(true);
            return;
        }

        try {
            const encoder = new TextEncoder();
            const commandString = `gamepad.${command.toLowerCase()}\r\n`;
            await project.writer.write(encoder.encode(commandString));
            console.log(`Sent command: ${commandString.trim()}`);
        } catch (error) {
            console.error("Error sending command:", error);
            setMessage("Error sending command: " + error.message);
            setSnackbarOpen(true);
        }
    };

    // Handle button press
    const handleButtonPress = (command) => {
        setIsPressed(prev => ({ ...prev, [command]: true }));
        sendCommand(command);

        if (continuousMode) {
            intervalRef.current = setInterval(() => {
                sendCommand(command);
            }, 100); // Send command every 100ms in continuous mode
        }
    };

    // Handle button release
    const handleButtonRelease = (command) => {
        setIsPressed(prev => ({ ...prev, [command]: false }));
        
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
        }

        if (continuousMode) {
            sendCommand("STOP"); // Send stop command when button is released in continuous mode
        }
    };

    // Keyboard event handlers
    useEffect(() => {
        const handleKeyDown = (event) => {
            const keyMap = {
                'ArrowUp': 'UP',
                'ArrowDown': 'DOWN',
                'ArrowLeft': 'LEFT',
                'ArrowRight': 'RIGHT',
                'Enter': 'CENTER',
                ' ': 'CENTER', // Spacebar
                'a': 'A',
                'b': 'B',
                'x': 'X',
                'y': 'Y',
                's': 'START',
                'p': 'SELECT',
                'Escape': 'STOP',
            };

            const command = keyMap[event.key];
            if (command && !isPressed[command]) {
                event.preventDefault();
                handleButtonPress(command);
            }
        };

        const handleKeyUp = (event) => {
            const keyMap = {
                'ArrowUp': 'UP',
                'ArrowDown': 'DOWN',
                'ArrowLeft': 'LEFT',
                'ArrowRight': 'RIGHT',
                'Enter': 'CENTER',
                ' ': 'CENTER',
                'a': 'A',
                'b': 'B',
                'x': 'X',
                'y': 'Y',
                's': 'START',
                'p': 'SELECT',
                'Escape': 'STOP',
            };

            const command = keyMap[event.key];
            if (command) {
                event.preventDefault();
                handleButtonRelease(command);
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        window.addEventListener('keyup', handleKeyUp);

        return () => {
            window.removeEventListener('keydown', handleKeyDown);
            window.removeEventListener('keyup', handleKeyUp);
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
        };
    }, [isPressed, continuousMode]);

    return (
        <Box sx={{ flexGrow: 1, height: "100vh", display: "flex", flexDirection: "column" }}>
            <BeeAppBar
                project={project}
                setProject={setProject}
                handleConnectSerial={handleConnectSerial}
                message={message}
            />
            
            <Container maxWidth="lg" sx={{ mt: 4, mb: 4, flexGrow: 1 }}>
                <Typography variant="h4" component="h1" gutterBottom align="center">
                    BeE Gamepad Controller
                </Typography>
                
                <Typography variant="body1" align="center" sx={{ mb: 3 }}>
                    Control your BeE board with this virtual gamepad. Use keyboard or click buttons.
                </Typography>

                {/* Connection Status */}
                <Paper sx={{ p: 2, mb: 3 }}>
                    <Typography variant="h6" gutterBottom>
                        Connection Status: {project.serialConnected ? "Connected" : "Disconnected"}
                    </Typography>
                    <FormControlLabel
                        control={
                            <Switch
                                checked={continuousMode}
                                onChange={(e) => setContinuousMode(e.target.checked)}
                            />
                        }
                        label="Continuous Mode (hold button to repeat)"
                    />
                </Paper>

                <Grid container spacing={4} justifyContent="center">
                    {/* D-Pad */}
                    <Grid item xs={12} md={6}>
                        <Card>
                            <CardContent>
                                <Typography variant="h6" align="center" gutterBottom>
                                    Direction Pad
                                </Typography>
                                <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 1, maxWidth: 200, mx: 'auto' }}>
                                    {gamepadButtons.map((button) => {
                                        const IconComponent = button.icon;
                                        return (
                                            <Box
                                                key={button.id}
                                                sx={{
                                                    gridRow: button.position.row + 1,
                                                    gridColumn: button.position.col + 1,
                                                }}
                                            >
                                                <Tooltip title={`${button.command} (${button.id === 'up' ? '↑' : button.id === 'down' ? '↓' : button.id === 'left' ? '←' : button.id === 'right' ? '→' : 'Enter/Space'})`}>
                                                    <IconButton
                                                        size="large"
                                                        onMouseDown={() => handleButtonPress(button.command)}
                                                        onMouseUp={() => handleButtonRelease(button.command)}
                                                        onMouseLeave={() => handleButtonRelease(button.command)}
                                                        sx={{
                                                            width: 60,
                                                            height: 60,
                                                            backgroundColor: isPressed[button.command] ? 'primary.main' : 'grey.200',
                                                            color: isPressed[button.command] ? 'white' : 'black',
                                                            '&:hover': {
                                                                backgroundColor: isPressed[button.command] ? 'primary.dark' : 'grey.300',
                                                            },
                                                        }}
                                                    >
                                                        <IconComponent />
                                                    </IconButton>
                                                </Tooltip>
                                            </Box>
                                        );
                                    })}
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>

                    {/* Action Buttons */}
                    <Grid item xs={12} md={6}>
                        <Card>
                            <CardContent>
                                <Typography variant="h6" align="center" gutterBottom>
                                    Action Buttons
                                </Typography>
                                <Grid container spacing={2} justifyContent="center">
                                    {actionButtons.map((button) => (
                                        <Grid item key={button.id}>
                                            <Tooltip title={`${button.command} (${button.id})`}>
                                                <Button
                                                    variant={isPressed[button.command] ? "contained" : "outlined"}
                                                    color={button.color}
                                                    size="large"
                                                    onMouseDown={() => handleButtonPress(button.command)}
                                                    onMouseUp={() => handleButtonRelease(button.command)}
                                                    onMouseLeave={() => handleButtonRelease(button.command)}
                                                    sx={{
                                                        width: 60,
                                                        height: 60,
                                                        borderRadius: '50%',
                                                        minWidth: 60,
                                                    }}
                                                >
                                                    {button.label}
                                                </Button>
                                            </Tooltip>
                                        </Grid>
                                    ))}
                                </Grid>
                            </CardContent>
                        </Card>
                    </Grid>

                    {/* Control Buttons */}
                    <Grid item xs={12}>
                        <Card>
                            <CardContent>
                                <Typography variant="h6" align="center" gutterBottom>
                                    Control Buttons
                                </Typography>
                                <Grid container spacing={2} justifyContent="center">
                                    {controlButtons.map((button) => {
                                        const IconComponent = button.icon;
                                        return (
                                            <Grid item key={button.id}>
                                                <Tooltip title={`${button.command} (${button.id === 'start' ? 's' : button.id === 'select' ? 'p' : 'Esc'})`}>
                                                    <Button
                                                        variant={isPressed[button.command] ? "contained" : "outlined"}
                                                        startIcon={<IconComponent />}
                                                        onMouseDown={() => handleButtonPress(button.command)}
                                                        onMouseUp={() => handleButtonRelease(button.command)}
                                                        onMouseLeave={() => handleButtonRelease(button.command)}
                                                        sx={{ minWidth: 120 }}
                                                    >
                                                        {button.label}
                                                    </Button>
                                                </Tooltip>
                                            </Grid>
                                        );
                                    })}
                                </Grid>
                            </CardContent>
                        </Card>
                    </Grid>
                </Grid>

                {/* Keyboard Instructions */}
                <Paper sx={{ p: 2, mt: 3 }}>
                    <Typography variant="h6" gutterBottom>
                        Keyboard Controls:
                    </Typography>
                    <Typography variant="body2">
                        • Arrow Keys: Direction control<br/>
                        • Enter/Space: Center button<br/>
                        • A, B, X, Y: Action buttons<br/>
                        • S: Start, P: Select, Esc: Stop
                    </Typography>
                </Paper>
            </Container>

            <Snackbar
                open={snackbarOpen}
                autoHideDuration={3000}
                onClose={() => setSnackbarOpen(false)}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
            >
                <Alert onClose={() => setSnackbarOpen(false)} severity={project.serialConnected ? "success" : "warning"}>
                    {message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default BeeGamepad;
