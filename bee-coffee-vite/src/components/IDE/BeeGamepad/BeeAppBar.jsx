import * as React from "react";
import AppBar from "@mui/material/AppBar";
import Box from "@mui/material/Box";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import Menu from "@mui/material/Menu";
import Container from "@mui/material/Container";
import Avatar from "@mui/material/Avatar";
import Button from "@mui/material/Button";
import MenuItem from "@mui/material/MenuItem";
import { Divider, Drawer, List, ListItem, ListItemButton, ListItemIcon, ListItemText, Tooltip } from "@mui/material";
import LogoutIcon from "@mui/icons-material/Logout";
import { deepOrange, grey } from "@mui/material/colors";
import { useNavigate } from "react-router-dom";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import UsbIcon from "@mui/icons-material/Usb";
import UsbOffIcon from "@mui/icons-material/UsbOff";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import FullscreenExitIcon from "@mui/icons-material/FullscreenExit";
import SportsEsportsIcon from "@mui/icons-material/SportsEsports";
import HomeIcon from "@mui/icons-material/Home";
import CodeIcon from "@mui/icons-material/Code";
import ExtensionIcon from "@mui/icons-material/Extension";

function BeeAppBar({ project, setProject, handleConnectSerial, message }) {
    const navigate = useNavigate();

    const [anchorElUser, setAnchorElUser] = React.useState(null);
    const [open, setOpen] = React.useState(false);
    const [fullScreen, setFullScreen] = React.useState(false);

    // Add event listener for fullscreen changes
    React.useEffect(() => {
        const handleFullscreenChange = () => {
            setFullScreen(!!document.fullscreenElement);
        };

        document.addEventListener("fullscreenchange", handleFullscreenChange);
        return () => {
            document.removeEventListener("fullscreenchange", handleFullscreenChange);
        };
    }, []);

    const handleFullScreen = () => {
        if (!fullScreen) {
            // Request fullscreen
            if (document.documentElement.requestFullscreen) {
                document.documentElement.requestFullscreen();
            }
        } else {
            // Exit fullscreen
            if (document.exitFullscreen) {
                document.exitFullscreen();
            }
        }
    };

    const handleOpenUserMenu = (event) => {
        setAnchorElUser(event.currentTarget);
    };

    const handleCloseUserMenu = () => {
        setAnchorElUser(null);
    };

    const toggleDrawer = (newOpen) => () => {
        setOpen(newOpen);
    };

    const handleDisconnectSerial = async () => {
        if (project.port) {
            try {
                if (project.writer) {
                    await project.writer.releaseLock();
                }
                if (project.reader) {
                    await project.reader.releaseLock();
                }
                await project.port.close();
            } catch (error) {
                console.error("Error disconnecting:", error);
            }
        }
        
        setProject((prev) => ({
            ...prev,
            serialConnected: false,
            port: null,
            writer: null,
            reader: null,
        }));
    };

    const menuItems = [
        { text: "Home", icon: <HomeIcon />, path: "/" },
        { text: "IDE Selection", icon: <CodeIcon />, path: "/play" },
        { text: "Block Programming", icon: <ExtensionIcon />, path: "/play/bee-ide" },
        { text: "Python", icon: <CodeIcon />, path: "/play/python" },
        { text: "Scratch", icon: <ExtensionIcon />, path: "/play/scratch" },
        { text: "IoT Dashboard", icon: <ExtensionIcon />, path: "/play/iot" },
    ];

    const DrawerList = (
        <Box sx={{ width: 250 }} role="presentation" onClick={toggleDrawer(false)}>
            <Box sx={{ p: 2, display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                <Typography variant="h6">BeE Gamepad</Typography>
                <IconButton onClick={toggleDrawer(false)}>
                    <CloseIcon />
                </IconButton>
            </Box>
            <Divider />
            <List>
                {menuItems.map((item) => (
                    <ListItem key={item.text} disablePadding>
                        <ListItemButton onClick={() => navigate(item.path)}>
                            <ListItemIcon>{item.icon}</ListItemIcon>
                            <ListItemText primary={item.text} />
                        </ListItemButton>
                    </ListItem>
                ))}
            </List>
        </Box>
    );

    return (
        <AppBar position="static" sx={{ backgroundColor: "#1976d2" }}>
            <Container maxWidth="xl">
                <Toolbar disableGutters>
                    {/* Mobile menu button */}
                    <Box sx={{ flexGrow: 0, display: { xs: "flex", md: "none" } }}>
                        <IconButton
                            size="large"
                            aria-label="menu"
                            onClick={toggleDrawer(true)}
                            color="inherit"
                        >
                            <MenuIcon />
                        </IconButton>
                    </Box>

                    {/* Logo and title */}
                    <SportsEsportsIcon sx={{ display: { xs: "none", md: "flex" }, mr: 1 }} />
                    <Typography
                        variant="h6"
                        noWrap
                        component="a"
                        href="/"
                        sx={{
                            mr: 2,
                            display: { xs: "none", md: "flex" },
                            fontFamily: "monospace",
                            fontWeight: 700,
                            letterSpacing: ".3rem",
                            color: "inherit",
                            textDecoration: "none",
                        }}
                    >
                        BeE Gamepad
                    </Typography>

                    {/* Mobile logo */}
                    <SportsEsportsIcon sx={{ display: { xs: "flex", md: "none" }, mr: 1 }} />
                    <Typography
                        variant="h5"
                        noWrap
                        component="a"
                        href="/"
                        sx={{
                            mr: 2,
                            display: { xs: "flex", md: "none" },
                            flexGrow: 1,
                            fontFamily: "monospace",
                            fontWeight: 700,
                            letterSpacing: ".3rem",
                            color: "inherit",
                            textDecoration: "none",
                        }}
                    >
                        BeE Gamepad
                    </Typography>

                    {/* Desktop menu */}
                    <Box sx={{ flexGrow: 1, display: { xs: "none", md: "flex" } }}>
                        {menuItems.slice(0, 4).map((item) => (
                            <Button
                                key={item.text}
                                onClick={() => navigate(item.path)}
                                sx={{ my: 2, color: "white", display: "block" }}
                            >
                                {item.text}
                            </Button>
                        ))}
                    </Box>

                    {/* Status message */}
                    {message && (
                        <Typography variant="body2" sx={{ mr: 2, color: "white" }}>
                            {message}
                        </Typography>
                    )}

                    {/* Action buttons */}
                    <Box sx={{ flexGrow: 0, display: "flex", alignItems: "center", gap: 1 }}>
                        {/* Serial connection button */}
                        <Tooltip title={project.serialConnected ? "Disconnect from BeE Board" : "Connect to BeE Board"}>
                            <IconButton
                                onClick={project.serialConnected ? handleDisconnectSerial : handleConnectSerial}
                                sx={{
                                    color: "white",
                                    backgroundColor: project.serialConnected ? "success.main" : "grey.600",
                                    "&:hover": {
                                        backgroundColor: project.serialConnected ? "success.dark" : "grey.700",
                                    },
                                }}
                            >
                                {project.serialConnected ? <UsbIcon /> : <UsbOffIcon />}
                            </IconButton>
                        </Tooltip>

                        {/* Fullscreen button */}
                        <Tooltip title={fullScreen ? "Exit Fullscreen" : "Enter Fullscreen"}>
                            <IconButton onClick={handleFullScreen} sx={{ color: "white" }}>
                                {fullScreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
                            </IconButton>
                        </Tooltip>
                    </Box>
                </Toolbar>
            </Container>

            {/* Mobile drawer */}
            <Drawer open={open} onClose={toggleDrawer(false)}>
                {DrawerList}
            </Drawer>
        </AppBar>
    );
}

export default BeeAppBar;
